name: PR - Validate and Build

permissions: read-all

on:
  pull_request:
    branches:
      - develop
    types:
      - opened
      - reopened
      - synchronize
      - labeled

  # Allows to run this workflow manually from the Actions tab
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref }}
  cancel-in-progress: true

jobs:

  pre-checks:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@1d96c772d19495a3b5c517cd2bc0cb401ea0529f # v4.1.3
      - name: Check dependabot build
        uses: ./.github/actions/check-ext-build
      - name: pre-commit
        uses: Alfresco/alfresco-build-tools/.github/actions/pre-commit@b3aa3308ae1c2e2140c8a6d12f3001cd644313f7 # v5.23.0
        with:
          skip_checkout: true
      - name: Ensure SHA pinned actions
        uses: zgosalvez/github-actions-ensure-sha-pinned-actions@ba37328d4ea95eaf8b3bd6c6cef308f709a5f2ec # v3.0.3

  build:
    runs-on: ubuntu-latest
    needs: pre-checks
    steps:

      - name: Checkout repository
        uses: actions/checkout@1d96c772d19495a3b5c517cd2bc0cb401ea0529f # v4.1.3

      - uses: actions/cache@0c45773b623bea8c8e75f6c82b208c3cf94ea4f9 # v4.0.2
        with:
          path: ~/.m2/repository
          key: ${{ runner.os }}-maven-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-maven-

      - name: Setup Java JDK 21
        uses: actions/setup-java@99b8673ff64fbf99d8d325f52d9a5bdedb8483e9 # 4.2.1
        with:
          java-version: 21
          distribution: 'temurin'

      - name: Set preview version
        if: ${{ contains(github.event.pull_request.labels.*.name, 'preview') }}
        run: |
          GITHUB_PR_NUMBER=PR-${{ github.event.pull_request.number }}
          echo 0.0.1-${GITHUB_PR_NUMBER}-${GITHUB_RUN_NUMBER}-SNAPSHOT > VERSION

      - name: Set VERSION env variable
        if: ${{ contains(github.event.pull_request.labels.*.name, 'preview') }}
        run: |
          VERSION=$(cat VERSION)
          echo set VERSION=$VERSION
          echo "VERSION=$VERSION" >> $GITHUB_ENV

      - name: Update pom files to the new version
        if: ${{ contains(github.head_ref, 'preview') || contains(github.event.pull_request.labels.*.name, 'preview') }}
        run: mvn -B versions:set -DnewVersion=$VERSION -DprocessAllModules=true -DgenerateBackupPoms=false

      - name: Define Maven Command
        id: define_maven_command
        shell: bash
        run: |
          if [ $DO_PUSH == 'true' ]
          then
            echo "command=deploy" >> $GITHUB_OUTPUT
          else
            echo "command=verify" >> $GITHUB_OUTPUT
          fi
        env:
          DO_PUSH: ${{ contains(github.event.pull_request.labels.*.name, 'preview') }}

      - name: Build and Test with Maven (and maybe Deploy)
        shell: bash
        run: mvn ${{ steps.define_maven_command.outputs.command }} ${{ env.MAVEN_CLI_OPTS}}
        env:
          MAVEN_CLI_OPTS: --show-version --no-transfer-progress --settings settings.xml
          MAVEN_USERNAME: ${{ secrets.NEXUS_USERNAME }}
          MAVEN_PASSWORD: ${{ secrets.NEXUS_PASSWORD }}

      - name: Echo Longest Test Run
        uses: ./.github/actions/echo-longest-run
