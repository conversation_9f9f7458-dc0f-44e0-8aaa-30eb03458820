/*
 * Copyright 2010-2020 Alfresco Software, Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.activiti.api.model.shared;

import java.io.Serializable;

public abstract class Result<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    private Payload payload;
    private T entity;

    public Result() {
    }

    public Result(Payload payload,
                  T entity) {
        this.payload = payload;
        this.entity = entity;
    }

    public Payload getPayload() {
        return payload;
    }

    public T getEntity() {
        return entity;
    }
}
