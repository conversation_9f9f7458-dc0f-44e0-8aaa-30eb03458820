name: Check Ext build
description: Checks if the build is a dependabot build or external contribution

runs:
  using: composite
  steps:

    - name: Check build
      env:
        SENDER_LOGIN: ${{ github.event.sender.login }}
        EVENT_ACTION: ${{ github.event.action }}
        LABEL: ${{ github.event.label.name }}
        PR_REPO_NAME: ${{ github.event.pull_request.head.repo.full_name }}
        REPO_NAME: ${{ github.repository }}
      shell: bash
      run: |
        if [[ $SENDER_LOGIN != 'dependabot[bot]' && (-z $PR_REPO_NAME || $PR_REPO_NAME == $REPO_NAME) ]]
        then
          exit 0
        fi

        if [[ $EVENT_ACTION == 'labeled' && $LABEL == 'CI' ]]
        then
          echo "This workflow will run with actor who labeled the PR"
        else
          echo "This workflow run was triggered by dependabot or an external contributor. Check and validate the changes, then add the 'CI' label to the pull request to trigger this workflow."
          exit 1
        fi
