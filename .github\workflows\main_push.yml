name: Push - <PERSON><PERSON><PERSON>, Build and Release

permissions:
  actions: write
  checks: write
  contents: write

on:
  push:
    branches:
      - develop

  # Allows to run this workflow manually from the Actions tab
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref_name }}
  cancel-in-progress: true

jobs:

  pre-checks:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@1d96c772d19495a3b5c517cd2bc0cb401ea0529f # v4.1.3
      - name: pre-commit
        uses: Alfresco/alfresco-build-tools/.github/actions/pre-commit@b3aa3308ae1c2e2140c8a6d12f3001cd644313f7 # v5.23.0
        with:
          skip_checkout: true
      - name: Ensure SHA pinned actions
        uses: zgosalvez/github-actions-ensure-sha-pinned-actions@ba37328d4ea95eaf8b3bd6c6cef308f709a5f2ec # v3.0.3

  build:
    runs-on: ubuntu-latest
    needs: pre-checks
    steps:

      - name: Checkout repository
        uses: actions/checkout@1d96c772d19495a3b5c517cd2bc0cb401ea0529f # v4.1.3
        with:
          token: ${{ secrets.BOT_GITHUB_TOKEN }}

      - uses: actions/cache@0c45773b623bea8c8e75f6c82b208c3cf94ea4f9 # v4.0.2
        with:
          path: ~/.m2/repository
          key: ${{ runner.os }}-maven-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-maven-

      - name: Setup Java JDK 21
        uses: actions/setup-java@99b8673ff64fbf99d8d325f52d9a5bdedb8483e9 # 4.2.1
        with:
          java-version: 21
          distribution: 'temurin'

      - uses: Alfresco/alfresco-build-tools/.github/actions/update-pom-to-next-pre-release@b3aa3308ae1c2e2140c8a6d12f3001cd644313f7 # v5.23.0
        id: update-pom-to-next-version

      - name: Update VERSION file
        run: |
          echo ${{steps.update-pom-to-next-version.outputs.next-prerelease}} > VERSION

      - name: Set VERSION env variable
        run: |
          VERSION=$(cat VERSION)
          echo set VERSION=$VERSION
          echo "VERSION=$VERSION" >> $GITHUB_ENV

      - name: Build and Test with Maven (and maybe Deploy)
        shell: bash
        run: mvn deploy ${{ env.MAVEN_CLI_OPTS}}
        env:
          MAVEN_CLI_OPTS: --show-version --no-transfer-progress --settings settings.xml
          MAVEN_USERNAME: ${{ secrets.NEXUS_USERNAME }}
          MAVEN_PASSWORD: ${{ secrets.NEXUS_PASSWORD }}

      - name: Echo Longest Test Run
        uses: ./.github/actions/echo-longest-run

      - name: Configure git user
        run: |
          git config --global user.name $GITHUB_USERNAME
          git config --global user.email $<EMAIL>
        env:
          GITHUB_USERNAME: ${{ secrets.BOT_GITHUB_USERNAME }}

      - name: Create release tag
        run: |
          git commit -am "Release $VERSION" --allow-empty
          git tag -fa $VERSION -m "Release version $VERSION"
          git push -f -q origin $VERSION

      - name: Get branch name
        uses: Alfresco/alfresco-build-tools/.github/actions/get-branch-name@b3aa3308ae1c2e2140c8a6d12f3001cd644313f7 # v5.23.0

      - name: Propagate
        uses: Alfresco/alfresco-build-tools/.github/actions/jx-updatebot-pr@b3aa3308ae1c2e2140c8a6d12f3001cd644313f7 # v5.23.0
        if: ${{ env.BRANCH_NAME == 'develop' }}
        env:
          DEVELOPMENT_BRANCH: ${{ github.ref_name }}
        with:
          version: ${{ env.VERSION }}
          auto-merge: 'true'
          labels: ${{ env.DEVELOPMENT_BRANCH }}
          base-branch-name: ${{ env.DEVELOPMENT_BRANCH }}
          git-username: ${{ secrets.BOT_GITHUB_USERNAME }}
          git-token: ${{ secrets.BOT_GITHUB_TOKEN }}
          git-author-name: ${{ secrets.BOT_GITHUB_USERNAME }}

  notify:
    runs-on: ubuntu-latest
    needs: build
    if: always() && failure()
    steps:
      - name: Slack Notification
        uses: Alfresco/alfresco-build-tools/.github/actions/send-slack-notification@b3aa3308ae1c2e2140c8a6d12f3001cd644313f7 # v5.23.0
        with:
          channel-id: 'eng-hxp-studio-activiti-gh-notifs'
          token: ${{ secrets.SLACK_NOTIFICATION_BOT_TOKEN }}
