/*
 * Copyright 2010-2020 Alfresco Software, Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.activiti.api.process.model.builders;


import org.activiti.api.process.model.ProcessInstance;
import org.activiti.api.process.model.payloads.DeleteProcessPayload;

public class DeleteProcessPayloadBuilder {

    private String processInstanceId;
    private String reason;

    public DeleteProcessPayloadBuilder withProcessInstanceId(String processDefinitionId) {
        this.processInstanceId = processDefinitionId;
        return this;
    }

    public DeleteProcessPayloadBuilder withReason(String reason) {
        this.reason = reason;
        return this;
    }

    public DeleteProcessPayloadBuilder withProcessInstance(ProcessInstance processInstance) {
        this.processInstanceId = processInstance.getId();
        return this;
    }

    public DeleteProcessPayload build() {
        return new DeleteProcessPayload(processInstanceId,
                                        reason);
    }
}
