name: "Dependency Graph Submission of Transitive Dependencies"

on:
  push:
    branches: [ "develop", "7.*.x" ]
jobs:
  submit:
    name: Submit
    runs-on: ubuntu-latest
    steps:
    - name: Checkout repository
      uses: actions/checkout@1d96c772d19495a3b5c517cd2bc0cb401ea0529f # v4.1.3
    - uses: actions/cache@0c45773b623bea8c8e75f6c82b208c3cf94ea4f9 # v4.0.2
      with:
          path: ~/.m2/repository
          key: ${{ runner.os }}-maven-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-maven-
    - name: Set up JDK 21
      uses: actions/setup-java@99b8673ff64fbf99d8d325f52d9a5bdedb8483e9 # v4.2.1
      with:
         java-version: '21'
         distribution: 'temurin'
    - name: Submit Dependency Graph
      uses: advanced-security/maven-dependency-submission-action@fcd7eab6b6d22946badc98d1e62665cdee93e0ae # v3.0.3
