name: Create Pre-release

on:
  push:
    tags:
      - '[0-9]+.[0-9]+.[0-9]+-alpha.[0-9]+'

jobs:
  create-prerelease:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@1d96c772d19495a3b5c517cd2bc0cb401ea0529f # v4.1.3

      - name: Create prerelease
        env:
          TAG: ${{ github.ref_name }}
          GITHUB_TOKEN: ${{ secrets.BOT_GITHUB_TOKEN }}
        run: |
          gh release create $TAG --prerelease -t $TAG
