/*
 * Copyright 2010-2020 Alfresco Software, Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.activiti.api.model.shared.event;

import java.io.Serializable;

public interface RuntimeEvent<ENTITY_TYPE, EVENT_TYPE extends Enum<?>> extends Serializable {

    String getId();

    ENTITY_TYPE getEntity();

    Long getTimestamp();

    EVENT_TYPE getEventType();

    String getProcessInstanceId();

    String getParentProcessInstanceId();

    String getProcessDefinitionId();

    String getProcessDefinitionKey();

    Integer getProcessDefinitionVersion();

    String getBusinessKey();

}
